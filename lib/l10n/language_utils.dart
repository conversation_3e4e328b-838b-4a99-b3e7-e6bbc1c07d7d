import 'package:and/app.dart';
import 'package:and/cache/cache_helper.dart';
import 'package:and/l10n/l10n.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

import 'language.dart';

class LanguageUtils {
  static String getLanguage(BuildContext context, String code) {
    late String languageName;
    Language language = Language.getLanguage(code);
    switch (language) {
      case Language.fsLan:
        languageName = context.l10n.followerSystemLanguage;
        break;
      case Language.zhCN:
        languageName = context.l10n.simplifiedChinese;
        break;
      case Language.zhTW:
        languageName = context.l10n.traditionalChinese;
        break;
      case Language.enUS:
        languageName = context.l10n.english;
        break;
      case Language.jaJP:
        languageName = context.l10n.japanese;
        break;
    }
    return languageName;
  }

  static Locale getDeviceLocale() {
    var locale = Get.deviceLocale;
    if (locale == null) {
      return Locale("en", "US");
    }
    // 处理繁体中文
    if (locale.scriptCode == "Hant") {
      return Locale.fromSubtags(
          languageCode: "zh", scriptCode: "Hant", countryCode: "TW");
    }

    // 处理简体粤语
    if (locale.languageCode == "yue") {
      return Locale("zh", "CN");
    }

    // 检查语言是否在支持的列表中，如果不在则默认使用英语
    if (!<String>["en", "ja", "zh"].contains(locale.languageCode)) {
      return Locale("en", "US");
    }

    return locale;
  }

  static Locale getLocale() {
    Locale? locale;
    final currentCountryCode = CacheHelper.currentCountryCode;
    if (currentCountryCode != null &&
        currentCountryCode.isNotEmpty &&
        currentCountryCode != Language.fsLan.countryCode) {
      locale = Locale(
          currentCountryCode.split('-')[0], currentCountryCode.split('-')[1]);
    }
    return locale ?? getDeviceLocale();
  }

  static void updateLocale(String countryCode, {bool isL10n = true}) {
    if (countryCode == getLocale().countryCode) {
      return;
    }
    CacheHelper.saveCurrentCountryCode(countryCode);
    List<String> lang = countryCode.split('-');
    var locale = (countryCode == Language.fsLan.countryCode)
        ? getLocale()
        : Locale(lang[0], lang[1]);
    Get.updateLocale(locale);
    if (isL10n) {
      AppState.setting.changeLocale?.call();
    }
  }

  static String languageCode(Locale local) {
    if (local.languageCode == "zh") {
      if (local.countryCode == "CN") {
        return Language.zhCN.countryCode;
      }
      return Language.zhTW.countryCode;
    } else if (local.languageCode == "en") {
      return Language.enUS.countryCode;
    } else if (local.languageCode == "ja") {
      return Language.jaJP.countryCode;
    }
    return '${local.languageCode}-${local.countryCode}';
  }

  static String language() {
    return languageCode(getLocale());
  }
}
