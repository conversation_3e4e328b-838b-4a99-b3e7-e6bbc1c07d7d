import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/wk_system_account.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/chat/chat_page.dart';
import 'package:and/module/user/user_info_logic.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/refresh/load_state.dart';
import 'package:and/widget/refresh/refresh_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';

class SystemUserPage extends StatefulWidget {
  final WKChannel channel;

  const SystemUserPage({super.key, required this.channel});

  @override
  State<StatefulWidget> createState() {
    return _SystemUserPageState();
  }
}

class _SystemUserPageState extends State<SystemUserPage> {
  late UserInfoArgument argument = UserInfoArgument.fromGet();
  late UserInfoLogic logic = Get.find<UserInfoLogic>(tag: argument.getTag());

  @override
  Widget build(BuildContext context) {
    return Scaffold(appBar: AppBar(), body: _buildContent());
  }

  Widget _buildContent() {
    return Column(
      children: [
        _buildInfo(),
        Container(height: 10),
        _buildFunctionInfo(),
        Container(height: 10),
        _buildSendMessage()
      ],
    );
  }

  Widget _buildInfo() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      child: Row(children: [
        AvatarWidget(
          widget.channel.avatarUrl,
          name: widget.channel.displayName,
          size: 60,
          fontSize: 18,
        ),
        SizedBox(width: 10),
        Column(children: [
          Text(widget.channel.displayName, style: TextStyles.fontSize18Medium),
        ])
      ]),
    );
  }

  Widget _buildFunctionInfo() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      child: Row(children: [
        Text(context.l10n.functionTitle, style: TextStyles.fontSize16Medium),
        SizedBox(width: 10),
        Expanded(
            child: Text(_getFunctionTips(),
                style: TextStyles.fontSize15Normal
                    .copyWith(color: DColor.secondaryTextColor))),
      ]),
    );
  }

  Widget _buildSendMessage() {
    return InkWell(
        onTap: () {
          ChatPage.open(
              channelID: logic.channelID, channelType: logic.channelType);
        },
        child: Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
            child: Column(children: [
              Center(
                child: Text(context.l10n.sendMessage,
                    style: TextStyles.fontSize18Medium
                        .copyWith(color: DColor.secondaryTextColor)),
              )
            ])));
  }

  String _getFunctionTips() {
    switch (logic.channelID) {
      case WKSystemAccount.systemTeam:
        return context.l10n.functionSystemTeamTips;
      case WKSystemAccount.systemFileHelper:
        return context.l10n.functionFileHelperTips;
    }
    return "";
  }
}
