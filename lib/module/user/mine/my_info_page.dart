import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/http/http_config.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/user_login_info_ext.dart';
import 'package:and/module/user/completeInfo/complete_user_info_logic.dart';
import 'package:and/module/user/mine/my_info_logic.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/clipboard_utils.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/simple_setting_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:oktoast/oktoast.dart';

class MyInfoPage extends StatefulWidget {
  const MyInfoPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MyInfoPageState();
  }
}

class _MyInfoPageState extends State<MyInfoPage> {
  late MyInfoLogic logic = Get.find<MyInfoLogic>();
  late var item = logic.data;
  late var inviteInfo = logic.inviteInfo;

  @override
  void dispose() {
    super.dispose();

    Get.delete<MyInfoLogic>();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(appBar: AppBar(), body: Obx(() => _buildContent()));
  }

  Widget _buildContent() {
    return Column(
      children: [_buildBaseInfo()],
    );
  }

  Widget _buildBaseInfo() {
    // final inviteLink = inviteInfo.value?.inviteUrl ?? "";
    final inviteCode =
        inviteInfo.value?.inviteCode ?? item.value?.inviteCode ?? '';
    int modifiableTime = inviteInfo.value?.modifiableTime ?? 0;

    final List<Widget> items = [
      SimpleSettingItemWidget(context.l10n.avatar,
          rightWidget: AvatarWidget(CommonHelper.getMyAvatarUrl(),
              name: CacheHelper.userProfile?.name,
              size: 40),
          onTap: () {
        Get.toNamed(RouteGet.myAvatar);
      }),
      SimpleSettingItemWidget(context.l10n.name, value: item.value?.name,
          onTap: () {
        logic.reName(context, item.value?.name);
      }),
      SimpleSettingItemWidget(context.l10n.signature,
          value: item.value?.signature, onTap: () {
        logic.modifySignature(context, item.value?.signature);
      }),
      SimpleSettingItemWidget(context.l10n.shortNo(context.l10n.appName),
          value: item.value?.shortNo, onTap: () {
        logic.modifyShortNo(context, item.value?.shortNo);
      }),
      SimpleSettingItemWidget(context.l10n.myQrcode,
          rightWidget: Image.asset(
            ImagePath.ic_qrcode,
            height: 30,
            color: DColor.secondaryTextColor,
          ), onTap: () {
        Get.toNamed(RouteGet.myQr);
      }),
      SimpleSettingItemWidget(context.l10n.inviteCode, value: inviteCode,
          onTap: () {
        if (modifiableTime == 0 ||
            DateTime.now().millisecondsSinceEpoch > modifiableTime) {
          logic.modifyInviteCode(context, inviteCode);
        } else {
          ClipboardUtils.copyToClipboard(inviteCode);
        }
      }),
      // SimpleSettingItemWidget(context.l10n.inviteLink, value: inviteLink,
      //     onTap: () {
      //   ClipboardUtils.copyToClipboard(inviteLink);
      // }),
      SimpleSettingItemWidget(context.l10n.mobile,
          value: item.value?.formatPhone, onTap: () {
        ClipboardUtils.copyToClipboard(item.value?.formatPhone ?? "");
      }),
      SimpleSettingItemWidget(context.l10n.signupEmailPlaceholder,
          value: item.value?.email ?? '', onTap: () async {
        final email = item.value?.email;
        if (email == null || email == "") {
          await Get.toNamed(RouteGet.completeUserInfo);
          logic.refreshData();
          return;
        }
        ClipboardUtils.copyToClipboard(email);
      }),
      SimpleSettingItemWidget(context.l10n.gender,
          value: item.value?.sex == 1 ? context.l10n.male : context.l10n.female,
          onTap: () {
        _showActionSheet(context);
      })
    ];

    return Expanded(
        child: ListView.builder(
            itemCount: items.length,
            itemBuilder: (context, index) {
              return items[index];
            }));
  }

  void _showActionSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
            color: Colors.white,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListView.separated(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: 2,
                separatorBuilder: (context, index) =>
                    Divider(height: 0.5, color: Colors.grey[50]),
                itemBuilder: (BuildContext context, int index) {
                  return ListTile(
                    title: Text(
                        index == 1 ? context.l10n.male : context.l10n.female,
                        textAlign: TextAlign.center),
                    onTap: () {
                      logic.updateGender(context, index);
                      Navigator.pop(context);
                    },
                  );
                },
              ),
              Divider(height: 0.5, color: Colors.grey[50]),
              SafeArea(
                  child: ListTile(
                title: Text(context.l10n.globalCancel,
                    style: TextStyle(
                        color: Colors.grey, fontWeight: FontWeight.w500),
                    textAlign: TextAlign.center),
                onTap: () => Navigator.pop(context),
              )),
            ],
          ),
        );
      },
    );
  }
}
