import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wukongimfluttersdk/type/const.dart';

import 'group_join_logic.dart';

class GroupJoinArgument {
  String groupNo;
  String authCode;

  GroupJoinArgument({required this.groupNo, required this.authCode});

  factory GroupJoinArgument.fromGet() {
    return (Get.arguments as GroupJoinArgument);
  }

  String getTag() {
    return groupNo;
  }

  static getTagFromGet() {
    return GroupJoinArgument.fromGet().getTag();
  }
}

class GroupJoinPage extends StatefulWidget {
  const GroupJoinPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _GroupJoinPageState();
  }

  static void open({required String groupNo, required String authCode}) {
    Get.toNamed(RouteGet.groupJoin,
        preventDuplicates: false,
        arguments: GroupJoinArgument(
          groupNo: groupNo,
          authCode: authCode,
        ));
  }
}

class _GroupJoinPageState extends State<GroupJoinPage> {
  late final GroupJoinArgument argument = GroupJoinArgument.fromGet();
  late GroupJoinLogic logic = Get.find<GroupJoinLogic>(tag: argument.getTag());
  late final channel = logic.data;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          leading: CloseButton(),
        ),
        body: SafeArea(child: Obx(() => _buildContent())));
  }

  Widget _buildContent() {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Column(children: [
        SizedBox(height: 20),
        Expanded(
          child: _buildGroupInfo(),
        ),
        _buildJoin(),
        SizedBox(height: 60)
      ]),
    );
  }

  Widget _buildGroupInfo() {
    var avatar = CommonHelper.getAvatarUrl(argument.groupNo,
        channelType: WKChannelType.group);

    return Column(children: [
      AvatarWidget(avatar, name: channel.value?.name, size: 80, fontSize: 18,),
      const SizedBox(height: 20),
      Text(channel.value?.name ?? '', style: TextStyles.fontSize18Normal),
      Text(context.l10n.joinGroupMemberCount(channel.value?.memberCount ?? 0),
          style: TextStyles.fontSize16Normal.copyWith(color: Colors.grey)),
    ]);
  }

  Widget _buildJoin() {
    return SizedBox(
        width: 200,
        child: SubmitButton(
            onPressed: () {
              logic.joinGroup();
            },
            text: context.l10n.joinGroupSubmit));
  }
}
