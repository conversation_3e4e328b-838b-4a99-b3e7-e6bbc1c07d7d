import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/wk_channel_member_role.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wx_channel_member_ext.dart';
import 'package:and/module/group/manager/group_manager_logic.dart';
import 'package:and/module/search/widget/search_input_widget.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/letter_index_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:super_sliver_list/super_sliver_list.dart';
import 'package:wukongimfluttersdk/entity/channel_member.dart';

class GroupManagerPage extends StatefulWidget {
  const GroupManagerPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _GroupManagerPageState();
  }
}

class _GroupManagerPageState extends State<GroupManagerPage> {
  late final GroupManagerArgument argument = GroupManagerArgument.fromGet();
  late GroupManagerLogic logic =
      Get.find<GroupManagerLogic>(tag: argument.getTag());
  final ScrollController scrollController = ScrollController();
  final ListController listController = ListController();

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar: AppBar(
            title: Text(_getTitle(context)),
            actions: _buildAppBarActions(context),
          ),
          body: Column(
            children: [
              Container(
                margin: EdgeInsets.symmetric(horizontal: 15),
                child: _buildSearchInput(),
              ),
              SizedBox(height: 10),
              Expanded(child: _buildMembersList()),
              if (logic.currentOperation.value != GroupManagerOperation.view)
                _buildBottomBar(context),
            ],
          ),
        ));
  }

  Widget _buildBottomBar(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
          left: 12,
          right: 12,
          top: 12,
          bottom: 12 + MediaQuery.of(context).padding.bottom),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Colors.grey[200]!)),
      ),
      child: Row(
        children: [
          Text(
            context.l10n.selectedCount(logic.selectedMemberIds.length),
            style: TextStyle(fontSize: 16),
          ),
          Spacer(),
          TextButton(
            style: TextButton.styleFrom(
              backgroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            onPressed: () {
              if (logic.currentOperation.value == GroupManagerOperation.add) {
                logic.addSelectedManagers();
              } else {
                logic.removeSelectedManagers();
              }
            },
            child: Text(
              context.l10n.confirmCount(logic.selectedMemberIds.length),
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildAppBarActions(BuildContext context) {
    switch (logic.currentOperation.value) {
      case GroupManagerOperation.view:
        return [
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: IconButton(
              icon: const Icon(Icons.more_horiz),
              onPressed: () => _showViewModeBottomSheet(context),
            ),
          ),
        ];
      case GroupManagerOperation.add:
      case GroupManagerOperation.remove:
        return [
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: TextButton(
              onPressed: () =>
                  logic.changeOperation(GroupManagerOperation.view),
              child: Text(
                context.l10n.globalCancel,
                style: const TextStyle(color: Colors.black),
              ),
            ),
          ),
        ];
    }
  }

  Widget _buildSearchInput() {
    return SearchInputWidget(
      searchPlaceHolder: context.l10n.search,
      showImage: false,
      autofocus: false,
      onKeyChanged: (key) {
        logic.search(key);
      },
      onFieldSubmitted: (key) {
        logic.search(key);
      },
    );
  }

  Widget _buildMembersList() {
    return Stack(
      children: [
        _buildGroupedList(),
        Positioned(
          right: 5,
          top: 0,
          bottom: 0,
          child: Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
              margin: const EdgeInsets.symmetric(horizontal: 8),
              child: AlphabetScrollbar(
                selectedLetterSize: 30,
                factor: 0,
                selectedLetterAdditionalSpace: 30,
                onLetterChange: (value) {
                  int position = logic.letters.indexWhere((e) => e == value);
                  if (position >= 0) {
                    listController.jumpToItem(
                      index: position,
                      scrollController: scrollController,
                      alignment: 0,
                    );
                  }
                },
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGroupedList() {
    return CustomScrollView(
      controller: scrollController,
      slivers: [
        // 添加群主信息显示
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...logic.allMembers
                    .where((m) => m.role == WKChannelMemberRole.admin)
                    .map((admin) => ListTile(
                          leading: SizedBox(
                            width: 38,
                            child: Row(
                              children: [
                                AvatarWidget(
                                  CommonHelper.getAvatarUrl(admin.memberUID),
                                  name: admin.memberName,
                                  size: 30,
                                  fontSize: 10,
                                ),
                                const SizedBox(width: 8),
                              ],
                            ),
                          ),
                          title: Row(
                            children: [
                              Text(admin.displayName),
                              const SizedBox(width: 8),
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                decoration: BoxDecoration(
                                  color: DColor.primaryColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  context.l10n.groupOwner,
                                  style: TextStyle(fontSize: 12, color: DColor.primaryColor),
                                ),
                              ),
                            ],
                          ),
                        )),
                const Divider(height: 10),
              ],
            ),
          ),
        ),
        SliverPadding(
          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          sliver: SuperSliverList(
            listController: listController,
            delegate: SliverChildBuilderDelegate((context, index) {
              String letter = logic.letters[index];
              List<WKChannelMember> members =
                  logic.groupedMembers[letter] ?? [];
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
                    child: Text(
                      letter,
                      style: TextStyles.fontSize14Bold
                          .copyWith(color: DColor.primaryColor),
                    ),
                  ),
                  ...members.map((member) => _buildMemberItem(member)),
                ],
              );
            }, childCount: logic.letters.length),
          ),
        )
      ],
    );
  }

  Widget _buildMemberItem(WKChannelMember member) {
    final bool isManager = member.role == WKChannelMemberRole.manager;
    final bool isAdmin = member.role == WKChannelMemberRole.admin;
    final bool isAddMode = logic.currentOperation.value == GroupManagerOperation.add;
    final bool isSelected = logic.selectedMemberIds.contains(member.memberUID) || (isManager && isAddMode);
    final bool isSelectionMode = logic.currentOperation.value != GroupManagerOperation.view;

    return ListTile(
      leading: isSelectionMode
          ? SizedBox(
              width: 90,
              child: Row(
                children: [
                  Checkbox(
                    value: isSelected,
                    onChanged: isAddMode && isManager
                        ? null
                        : (bool? value) {
                            logic.toggleSelection(member.memberUID);
                          },
                  ),
                  const SizedBox(width: 8),
                  AvatarWidget(
                    CommonHelper.getAvatarUrl(member.memberUID),
                    name: member.memberName,
                    size: 30,
                    fontSize: 10,
                  ),
                ],
              ),
            )
          : SizedBox(
              width: 38,
              child: Row(
                children: [
                  AvatarWidget(
                    CommonHelper.getAvatarUrl(member.memberUID),
                    name: member.memberName,
                    size: 30,
                    fontSize: 10,
                  ),
                  const SizedBox(width: 8),
                ],
              ),
            ),
      title: Row(
        children: [
          Text(member.displayName),
          const SizedBox(width: 8),
          if (isAdmin)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: DColor.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                context.l10n.groupOwner,
                style: TextStyle(fontSize: 12, color: DColor.primaryColor),
              ),
            )
          else if (isManager)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: DColor.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                context.l10n.groupAdmin,
                style: TextStyle(fontSize: 12, color: DColor.primaryColor),
              ),
            ),
        ],
      ),
      onTap: () {
        if (isSelectionMode && !(isAddMode && isManager)) {
          logic.toggleSelection(member.memberUID);
        }
      },
    );
  }

  String _getTitle(BuildContext context) {
    switch (logic.currentOperation.value) {
      case GroupManagerOperation.view:
        return context.l10n.groupAdmin;
      case GroupManagerOperation.add:
        return context.l10n.addGroupAdmin;
      case GroupManagerOperation.remove:
        return context.l10n.deleteGroupAdmin;
    }
  }

  void _showViewModeBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => SafeArea(
          child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildOption(
              context,
              title: context.l10n.addGroupAdmin,
              icon: null,
              onTap: () {
                Get.back();
                logic.changeOperation(GroupManagerOperation.add);
              },
            ),
            Divider(height: 0.5, color: Colors.grey[100]),
            _buildOption(
              context,
              title: context.l10n.deleteGroupAdmin,
              icon: null,
              onTap: () {
                Get.back();
                logic.changeOperation(GroupManagerOperation.remove);
              },
            ),
            Divider(height: 0.5, color: Colors.grey[100]),
            Container(
              padding: EdgeInsets.only(bottom: 0),
              child: _buildCancelButton(context),
            ),
          ],
        ),
      )),
    );
  }

  Widget _buildCancelButton(BuildContext context) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(vertical: 4),
      title: Center(
        child: Text(
          context.l10n.globalCancel,
          style: TextStyle(color: Colors.grey[600], fontSize: 16),
        ),
      ),
      onTap: () => Get.back(),
    );
  }

  Widget _buildOption(
    BuildContext context, {
    required String title,
    required IconData? icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 4),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [Text(title, style: const TextStyle(fontSize: 16))],
      ),
      onTap: onTap,
    );
  }
}
