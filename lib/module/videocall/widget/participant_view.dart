import 'package:flutter/material.dart';
import 'package:livekit_client/livekit_client.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/utils/common_helper.dart';

class ParticipantView extends StatelessWidget {
  final Participant participant;
  final bool isLocal;
  final bool isVideoCall;
  final String displayName;
  final String channelId;
  final int channelType;

  const ParticipantView({
    super.key,
    required this.participant,
    required this.isLocal,
    required this.isVideoCall,
    required this.displayName,
    required this.channelId,
    required this.channelType,
  });

  @override
  Widget build(BuildContext context) {
    final videoTrack = participant.videoTrackPublications.isNotEmpty
        ? participant.videoTrackPublications.first
        : null;
    final audioTrack = participant.audioTrackPublications.isNotEmpty
        ? participant.audioTrackPublications.first
        : null;
    
    // 检查是否有屏幕共享轨道
    final screenShareTrack = participant.videoTrackPublications
        .where((pub) => pub.source == TrackSource.screenShareVideo)
        .firstOrNull;
    
    final hasVideoTrack = videoTrack != null &&
        (isLocal || videoTrack.subscribed) &&
        !videoTrack.muted;
    final hasScreenShare = screenShareTrack != null &&
        (isLocal || screenShareTrack.subscribed) &&
        !screenShareTrack.muted;
    final isMuted = audioTrack == null ||
        !audioTrack.subscribed ||
        audioTrack.muted;

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          // 优先显示屏幕共享，其次是视频或头像
          if (hasScreenShare)
            VideoTrackRenderer(
              screenShareTrack.track as VideoTrack,
            )
          else if (isVideoCall && hasVideoTrack)
            VideoTrackRenderer(
              videoTrack.track as VideoTrack,
            )
          else
            Center(
              child: AvatarWidget(
                CommonHelper.getAvatarUrl(
                  channelId,
                  channelType: channelType,
                ),
                name: displayName,
                size: 120,
                fontSize: 35,
              ),
            ),
          
          // 用户名和麦克风状态
          Positioned(
            left: 8,
            bottom: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  Text(
                    displayName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                    ),
                  ),
                  if (!isLocal) ...[  // 只有非本地用户才显示麦克风图标
                    const SizedBox(width: 4),
                    Icon(
                      isMuted ? Icons.mic_off : Icons.mic,
                      color: Colors.white,
                      size: 16,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}