import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/user/mine/my_info_logic.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/simple_setting_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MyPage extends StatefulWidget {
  const MyPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _MyPageState();
  }
}

class _MyPageState extends State<MyPage> {
  late MyInfoLogic logic = Get.put<MyInfoLogic>(MyInfoLogic());

  @override
  void dispose() {
    super.dispose();

    Get.delete<MyInfoLogic>();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(body: Obx(() => _buildContent()));
  }

  Widget _buildContent() {
    return CustomScrollView(
      slivers: [
        SliverAppBar(
          pinned: true,
          expandedHeight: MediaQuery.of(context).size.width * 0.71,
          backgroundColor: Colors.transparent,
          flexibleSpace: FlexibleSpaceBar(
            background: _buildHeader(),
          ),
        ),
        SliverList(
          delegate: SliverChildListDelegate([
            SimpleSettingItemWidget(
              context.l10n.personalInfo,
              leftWidget: Container(
                decoration: BoxDecoration(
                  color: DColor.primaryColor,
                  shape: BoxShape.circle,
                ),
                padding: EdgeInsets.all(8),
                child:
                    Icon(Icons.person_outline, size: 24, color: Colors.white),
              ),
              onTap: () {
                Get.toNamed(RouteGet.myInfo);
              },
            ),
            SimpleSettingItemWidget(
              context.l10n.securityAndPrivacy,
              leftWidget: Container(
                decoration: BoxDecoration(
                  color: Color(0xFF8A2BE2),
                  shape: BoxShape.circle,
                ),
                padding: EdgeInsets.all(8),
                child: Icon(Icons.lock_outline, size: 24, color: Colors.white),
              ),
              onTap: () {
                Get.toNamed(RouteGet.privacyAndSecurity);
              },
            ),
            SimpleSettingItemWidget(
              context.l10n.newMessageNotification,
              icon: ImagePath.ic_mine_notify,
              onTap: () {
                Get.toNamed(RouteGet.pushSetting);
              },
            ),
            SimpleSettingItemWidget(
              context.l10n.universal,
              icon: ImagePath.ic_mine_setting,
              onTap: () {
                Get.toNamed(RouteGet.commonSetting);
              },
            ),
          ]),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(ImagePath.ic_my_bg),
            fit: BoxFit.cover,
          ),
        ),
        child: InkWell(
          onTap: () {
            Get.toNamed(RouteGet.myInfo);
          },
          child: Column(children: [
            Align(
              alignment: Alignment.centerRight,
              child: Padding(
                padding: EdgeInsets.only(right: 30, top: MediaQuery.of(context).padding.top + 20),
                child: InkWell(
                    onTap: () {
                      Get.toNamed(RouteGet.myInfo);
                    },
                    child: Image.asset(ImagePath.ic_qrcode,
                        height: 30, color: DColor.primaryTextColor)),
              ),
            ),
            SizedBox(height: 40),
            Center(
                child: Column(
              children: [
                AvatarWidget(
                  CommonHelper.getMyAvatarUrl(),
                  name: CacheHelper.userProfile?.name,
                  size: 100,
                  fontSize: 30,
                ),
                SizedBox(height: 10),
                Text(logic.data.value?.name ?? '',
                    style: TextStyles.fontSize18Bold),
              ],
            )),
            SizedBox(height: 20),
          ])));
  }
}
