import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/module/contact/widget/ui_channel_name_widget.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class UiChannelItem extends StatefulWidget {
  final WKChannel channel;
  final bool isSelected;
  final Function() onTap;

  const UiChannelItem({
    super.key,
    required this.channel,
    this.isSelected = false,
    required this.onTap,
  });

  @override
  State<StatefulWidget> createState() {
    return _UiChannelItemState();
  }
}

class _UiChannelItemState extends State<UiChannelItem> {
  @override
  Widget build(BuildContext context) {
    return InkWell(onTap: widget.onTap, child: _buildRow());
  }

  @override
  void didUpdateWidget(covariant UiChannelItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.channel != oldWidget.channel) {
      setState(() {});
    }
  }

  Widget _buildRow() {
    return Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        child: Row(
          children: [
            Stack(children: [
              AvatarWidget(
                widget.channel.avatarUrl,
                name: widget.channel.displayName,
                online: widget.channel.online == 1,
                lastOffline: widget.channel.lastOffline,
                size: 40,
                fontSize: 14,
              ),
              if (widget.isSelected)
                Positioned(
                    right: 0,
                    bottom: 0,
                    child: Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child:
                            Icon(Icons.check, color: Colors.white, size: 12)))
            ]),
            SizedBox(width: 10),
            Expanded(
                child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                  Row(
                    children: <Widget>[
                      Flexible(
                          child: UiChannelNameWidget(
                            channel: widget.channel,
                          ))
                    ],
                  ),
                ]))
          ],
        ));
  }

  String getChannelName() {
    String channelName = widget.channel.displayName;
    try {
      if (channelName == '') {
        WKIM.shared.channelManager.fetchChannelInfo(
            widget.channel.channelID, widget.channel.channelType);
      }
    } catch (e) {
      print('Error fetching channel name: $e');
    }
    return channelName;
  }
}
