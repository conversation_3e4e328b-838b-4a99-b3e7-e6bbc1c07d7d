import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_channel_ext.dart';
import 'package:and/model/friend_apply.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class UiFriendBlackItem extends StatefulWidget {
  final WKChannel channel;
  final Function() onTap;

  const UiFriendBlackItem({
    super.key,
    required this.channel,
    required this.onTap,
  });

  @override
  State<StatefulWidget> createState() {
    return _UiFriendBlackItemState();
  }
}

class _UiFriendBlackItemState extends State<UiFriendBlackItem> {

  @override
  Widget build(BuildContext context) {
    return InkWell(onTap: widget.onTap, child: _buildRow());
  }

  @override
  void didUpdateWidget(covariant UiFriendBlackItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.channel != oldWidget.channel) {
      setState(() {

      });
    }
  }

  Widget _buildRow() {
    return Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        child: Row(
          children: [
            AvatarWidget(
              widget.channel.avatarUrl,
              name: widget.channel.displayName,
              online: widget.channel.online == 1,
              lastOffline: widget.channel.lastOffline,
              size: 40,
              fontSize: 14,
            ),
            SizedBox(width: 10),
            Expanded(
                child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Text(
                            getChannelName(),
                            style: TextStyles.fontSize16Normal,
                            maxLines: 1,
                          )
                        ],
                      ),
                    ]))
          ],
        ));
  }

  String getChannelName() {
    String channelName = widget.channel.displayName;
    try {
      if (channelName == '') {
        WKIM.shared.channelManager
            .fetchChannelInfo(widget.channel.channelID, widget.channel.channelType);
      }
    } catch (e) {
      print('Error fetching channel name: $e');
    }
    return channelName;
  }
}
