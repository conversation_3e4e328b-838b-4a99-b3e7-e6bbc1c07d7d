import 'package:and/common/res/text_styles.dart';
import 'package:and/utils/format_utils.dart';
import 'package:and/utils/time_utils.dart';
import 'package:flutter/material.dart';

class PromptNewDayWidget extends StatelessWidget {
  final int timestamp;

  const PromptNewDayWidget({super.key, required this.timestamp});

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
        child: Text(
          TimeUtils.formatTimestampToDay(timestamp),
          style: TextStyles.fontSize15Normal,
        ));
  }
}
