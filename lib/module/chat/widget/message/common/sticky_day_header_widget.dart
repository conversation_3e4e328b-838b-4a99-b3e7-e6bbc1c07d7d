import 'package:and/common/res/text_styles.dart';
import 'package:and/utils/time_utils.dart';
import 'package:flutter/material.dart';

class StickyDayHeaderWidget extends StatelessWidget {
  final int timestamp;

  const StickyDayHeaderWidget({super.key, required this.timestamp});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.95),
            borderRadius: BorderRadius.circular(6),
            border: Border(
              bottom: BorderSide(
                color: Colors.grey.withValues(alpha: 0.2),
                width: 0.5,
              ),
            ),
          ),
          child: Text(
            TimeUtils.formatTimestampToDay(timestamp),
            style: TextStyles.fontSize15Normal,
          )),
    );
  }
}
