import 'package:and/common/res/colours.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/module/chat/widget/message/common/msg_status_widget.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:wukongimfluttersdk/model/wk_card_content.dart';
import 'package:wukongimfluttersdk/type/const.dart';

import 'message_content_renderer.dart';

class CardMessageRenderer extends MessageContentRenderer {
  @override
  Widget render(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    WKCardContent cardContent = msg.messageContent! as WKCardContent;
    return InkWell(
        onTap: () {
          UserInfoPage.open(
              channelID: cardContent.uid, vercode: cardContent.vercode);
        },
        child: Container(
            constraints: BoxConstraints(maxWidth: 200),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AvatarWidget(
                      CommonHelper.getAvatarUrl(cardContent.uid),
                      name: cardContent.name,
                      size: 50,
                      fontSize: 14,
                    ),
                    SizedBox(width: 10),
                    Expanded(
                        child: Text(cardContent.name,
                            style: TextStyles.fontSize15Normal))
                  ],
                ),
                Divider(color: DColor.divider),
                Row(mainAxisSize: MainAxisSize.min, children: [
                  Expanded(child: Text(context.l10n.userCard)),
                  MsgStatusWidget(
                      msg: msg,
                      onResendMsgTap: () {
                        callback.resendMessage(msg);
                      })
                ])
              ],
            )));
  }

  @override
  bool needContainer() {
    return true;
  }

  @override
  Widget reply(BuildContext context, WKMsg msg, MessageItemCallback callback) {
    var reply = msg.messageContent?.reply;
    if (reply == null) {
      return Container();
    }
    WKCardContent cardContent = reply.payload! as WKCardContent;
    return InkWell(
      onTap: () {
        UserInfoPage.open(
            channelID: cardContent.uid, vercode: cardContent.vercode);
      },
      child: super.reply(context, msg, callback),
    );
  }

  @override
  bool canRender(WKMsg msg) {
    return msg.contentType == WkMessageContentType.card;
  }
}
