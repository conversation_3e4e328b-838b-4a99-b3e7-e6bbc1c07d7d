import 'package:and/cache/cache_helper.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/search_user.dart';
import 'package:and/module/user/user_info_page.dart';
import 'package:and/utils/common_helper.dart';
import 'package:and/widget/avatar_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/type/const.dart';
import 'package:wukongimfluttersdk/wkim.dart';

class UiSearchUserItem extends StatefulWidget {
  final SearchUser msg;

  const UiSearchUserItem({
    super.key,
    required this.msg,
  });

  @override
  State<StatefulWidget> createState() {
    return _UiSearchUserItemState();
  }
}

class _UiSearchUserItemState extends State<UiSearchUserItem> {
  late Future<WKChannel?> _channelFuture;

  @override
  void initState() {
    super.initState();
    _channelFuture = getChannel();
  }

  @override
  void didUpdateWidget(covariant UiSearchUserItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.msg != oldWidget.msg) {
      _channelFuture = getChannel();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
         UserInfoPage.open(channelID: widget.msg.data?.uid ?? '');
      },
      child: Container(
          color: Colors.white,
          padding: const EdgeInsets.all(10),
          child: Row(
            children: [
              AvatarWidget(
                CommonHelper.getAvatarUrl(widget.msg.data?.uid ?? ''),
                name: widget.msg.data?.name,
                size: 50,
                fontSize: 14,
              ),
              SizedBox(width: 10),
              Expanded(
                  child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                    Row(
                      children: <Widget>[
                        Text(
                          widget.msg.data?.name ?? '',
                          style: TextStyles.fontSize18Normal,
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ])),
              FutureBuilder<WKChannel?>(
                future: _channelFuture,
                builder:
                    (BuildContext context, AsyncSnapshot<WKChannel?> snapshot) {
                  var isSelf = widget.msg.data?.uid == CacheHelper.uid;
                  var isFollow = snapshot.data?.follow == 1 &&
                      snapshot.data?.isDeleted == 0;
                  if (isSelf || isFollow) {
                    return Container();
                  }

                  return SubmitButton(
                    text: context.l10n.applyFriend,
                    onPressed: () {
                      CommonHelper.addFriend(context,
                          channelID: widget.msg.data?.uid ?? '',
                          vercode: widget.msg.data?.vercode ?? '');
                    },
                  );
                },
              ),
            ],
          )),
    );
  }

  Future<WKChannel?> getChannel() async {
    var channel = await WKIM.shared.channelManager
        .getChannel(widget.msg.data?.uid ?? '', WKChannelType.personal);
    return channel;
  }
}
