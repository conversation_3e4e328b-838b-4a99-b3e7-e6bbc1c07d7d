import 'dart:math';

import 'package:and/common/res/colours.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class AvatarWidget extends StatelessWidget {
  final String? url;
  final String? name;
  final double? size;
  final double? fontSize;
  final Color? backgroundColor;
  final bool? online;
  final Color? onlineStatusColor;
  final int? lastOffline;

  const AvatarWidget(this.url,
      {super.key,
      this.name,
      this.size,
      this.fontSize,
      this.backgroundColor,
      this.online,
      this.onlineStatusColor,
      this.lastOffline});

  String getFirstNickName() {
    if (name?.isEmpty ?? false) {
      return "";
    }
    return name?.substring(0, (min(2, name!.length))) ?? "";
  }

  Widget _defaultAvatar() {
    return CircleAvatar(
      backgroundColor: backgroundColor ?? DColor.primaryColor,
      child: Text(
        textAlign: TextAlign.center,
        getFirstNickName(),
        style: TextStyles.fontSize18Normal
            .copyWith(fontSize: fontSize ?? 15, color: Colors.white),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        ClipOval(child: _buildAvatar()),
        Positioned(
          right: 0,
          bottom: 0,
          child: _buildOnlineStatus(),
        )
      ],
    );
  }

  Widget _buildAvatar() {
    if (isNetwork()) {
      return CachedNetworkImage(
        imageUrl: url ?? "",
        placeholder: (context, url) => _defaultAvatar(),
        errorWidget: (context, url, error) => _defaultAvatar(),
        width: size ?? 50,
        height: size ?? 50,
        fit: BoxFit.cover,
      );
    } else if (isAsset()) {
      return Image.asset(
        url ?? "",
        width: size ?? 50,
        height: size ?? 50,
        fit: BoxFit.cover,
      );
    }
    return _defaultAvatar();
  }

  Widget _buildOnlineStatus() {
    if (!showOnlineStatus()) {
      return Container();
    }
    return Container(
      width: (size ?? 50) * 0.25,
      height: (size ?? 50) * 0.25,
      decoration: BoxDecoration(
        color: onlineStatusColor ?? (online! ? Colors.green : Colors.grey),
        shape: BoxShape.circle,
        border: Border.all(color: Colors.white, width: 2),
      ),
    );
  }

  bool showOnlineStatus() {
    return (online ?? false) || ((lastOffline ?? 0) > 0);
  }

  bool isNetwork() {
    return url?.startsWith("http") ?? false;
  }

  bool isAsset() {
    return url?.startsWith("assets") ?? false;
  }
}
